"""
Run a trained policy with live data from OMNeT++.

This script loads a saved policy and uses it to interact with OMNeT++ in real-time.
It receives state information from OMNeT++, feeds it to the policy to get actions,
and sends those actions back to OMNeT++.

This implementation reuses the communication code from revamped_new.py and implements
data batching to collect observations for a period before computing actions.

It includes a data freshness check that rejects data that is too old based on the
simulation time from OMNeT++. This prevents processing stale data from the ZMQ buffer
and ensures that only recent, relevant data is used for decision making.
"""

import os
import sys
import time
import logging
import json
import glob
import numpy as np
import tensorflow as tf
import threading
from threading import Lock

# Import the TSNGCLEnvironment from revamped_new.py
from revamped_new import TSNGCLEnvironment

# Configure logging
logging.basicConfig(
    level=logging.ERROR,
    filename='run_policy.log',
    format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    filemode='a'
)

class PolicyRunner:
    """
    Class to run a trained policy with live data from OMNeT++.

    This class uses the TSNGCLEnvironment from revamped_new.py for communication
    with OMNeT++ and feeds the observations to a saved policy.

    It implements data batching to collect observations for a period (500 microseconds)
    before computing and sending actions.
    """

    def __init__(self, policy_path, port=5555, batch_interval_us=5000, action_rate_multiplier=10, max_data_age_us=10000):
        """
        Initialize the PolicyRunner.

        Args:
            policy_path: Path to the saved policy (should be path to the session directory containing actor_network)
            port: Port to use for ZeroMQ communication
            batch_interval_us: Interval in microseconds to batch data before computing actions
            action_rate_multiplier: Multiplier for the action rate (higher = fewer actions)
            max_data_age_us: Maximum age of data to process in microseconds (older data will be rejected)
        """
        logging.info(f"Initializing PolicyRunner with policy from {policy_path}")
        # print(f"Initializing PolicyRunner with policy from {policy_path}")

        # Load the saved actor network (not a full policy)
        # The policy_path should point to the session directory containing actor_network
        actor_network_path = os.path.join(policy_path, "actor_network")
        if not os.path.exists(actor_network_path):
            raise ValueError(f"Actor network not found at {actor_network_path}. Make sure policy_path points to the session directory.")

        self.actor_network = tf.compat.v2.saved_model.load(actor_network_path)
        logging.info("Actor network loaded successfully")
        # print("Actor network loaded successfully")

        # Create the environment for communication with OMNeT++
        self.env = TSNGCLEnvironment(port=port)

        # Force data_active to True to ensure we process data immediately when it arrives
        self.env.data_active = True
        self.env.last_data_received_time = time.time()

        # Set maximum data age for filtering stale data
        self.max_data_age_us = max_data_age_us
        self.last_valid_sim_time_us = 0  # Track the last valid simulation time

        # Add counters for monitoring data flow
        self.total_messages_received = 0
        self.last_message_count = 0
        self.last_message_check_time = time.time()

        # Add a flag to temporarily disable data age checking if no data is being received
        self.data_age_check_enabled = True

        # Initialize running flag
        self.running = True

        # Metrics tracking
        self.execution_start_time = time.time()
        self.actions_sent = 0
        self.observations_processed = 0
        self.data_rejections = 0
        self.connection_resets = 0

        # Schedule periodic connection checks
        self._schedule_connection_check()

        logging.info(f"TSNGCLEnvironment created with port {port}")
        # print(f"TSNGCLEnvironment created with port {port}")
        logging.info(f"Data stream set to active state with max data age of {max_data_age_us/1000:.1f} ms")
        # print(f"Data stream set to active state with max data age of {max_data_age_us/1000:.1f} ms")

        # Actor networks don't have policy state like full policies do
        # We'll use the actor network directly to get actions
        self.is_first_step = True

        # Episode tracking
        self.current_episode_step = 0
        self.max_episode_steps = 1000

        # Batching configuration
        self.batch_interval_us = batch_interval_us
        self.action_rate_multiplier = action_rate_multiplier
        self.observation_batches = []
        self.last_batch_process_time = 0  # in microseconds
        self.lock = Lock()
        self.running = True

        # Calculate and display the effective action rate
        action_interval_us = batch_interval_us * action_rate_multiplier
        action_interval_ms = action_interval_us / 1000
        actions_per_second = 1000 / action_interval_ms

        logging.info(f"Action rate configuration: {action_rate_multiplier}x multiplier")
        logging.info(f"Effective action interval: {action_interval_ms:.1f} ms ({actions_per_second:.2f} actions/sec)")

        # Start batch processing thread
        self.batch_thread = threading.Thread(target=self._batch_processor)
        self.batch_thread.daemon = True
        self.batch_thread.start()
        logging.info(f"Batch processor thread started - will process batches every {batch_interval_us} microseconds")
        # print(f"Batch processor started - will process batches every {batch_interval_us} microseconds")

        logging.info("PolicyRunner initialization complete")
        # print("PolicyRunner initialization complete")

    def _batch_processor(self):
        """Background thread to process batched observations and compute actions.

        This method runs in a background thread and processes batched observations
        every batch_interval_us microseconds. It computes actions based on the
        average observation over the batch period and sends them to OMNeT++.

        The method ensures that:
        1. Only batches with a minimum number of observations are processed
        2. Actions are only sent if the batch contains new data
        3. A minimum time interval is enforced between sending actions
        4. Actions are rate-limited to avoid overwhelming the system
        """
        current_time_str = time.strftime("%H:%M:%S", time.localtime())
        logging.info(f"[{current_time_str}] Batch processor thread started with {self.batch_interval_us} microsecond interval")
        # print(f"[{current_time_str}] Batch processor thread started with {self.batch_interval_us} microsecond interval")

        # Minimum number of observations required to process a batch
        min_batch_size = 3  # Increased from 1 to ensure we have enough data

        # Minimum time between sending actions (in microseconds)
        # Make this significantly longer than the batch interval to slow down action rate
        min_action_interval_us = self.batch_interval_us * self.action_rate_multiplier

        # Track the last time an action was sent (in simulation time microseconds)
        last_action_time_us = 0

        # Track the last observation processed to avoid duplicate actions
        last_observation_hash = None

        # Counter for skipped batches (for logging)
        skipped_batches = 0

        # Track action statistics
        total_actions_sent = 0
        action_start_time = time.time()
        last_stats_time = time.time()
        stats_interval = 30.0  # Log action stats every 30 seconds

        # Print the action rate
        action_interval_ms = min_action_interval_us / 1000
        # print(f"\n[{current_time_str}] Action rate limited to approximately one action every {action_interval_ms:.1f} milliseconds")
        # print(f"[{current_time_str}] This is approximately {1000/action_interval_ms:.1f} actions per second\n")

        while self.running:
            try:
                # Get current simulation time in microseconds from the environment
                # Default to system time if no simulation time is available yet
                current_sim_time_ms = 0
                using_system_time = False
                with self.env.lock:
                    for queue_id in self.env.expected_queues:
                        if queue_id in self.env.queue_states and self.env.queue_states[queue_id]:
                            current_sim_time_ms = self.env.queue_states[queue_id].get('current_sim_time', 0)
                            break

                # If we don't have valid simulation time yet, use system time as a fallback
                # This ensures we don't get stuck waiting for simulation time
                # Check for Unix timestamp (seconds since epoch) which would be around 1.7 billion for current dates
                # Real simulation time should be much smaller, typically in seconds or milliseconds
                if current_sim_time_ms == 0 or current_sim_time_ms > 1000000000:  # If zero or suspiciously large (Unix timestamp)
                    # Use system time in milliseconds as a fallback
                    using_system_time = True
                    current_sim_time_ms = time.time() * 1000
                    logging.debug("No valid simulation time available, using system time as fallback")

                    # Check if we've been using system time for too long
                    if not hasattr(self, 'system_time_start'):
                        self.system_time_start = time.time()
                    elif time.time() - self.system_time_start > 10:  # After 10 seconds of using system time
                        logging.warning(f"Using system time for {time.time() - self.system_time_start:.1f} seconds - no valid simulation time from OMNeT++")
                        if (time.time() - self.system_time_start) % 30 < 0.5:  # Log to console every 30 seconds
                            logging.info(f"WARNING: Using system time for {time.time() - self.system_time_start:.1f} seconds - no valid simulation time from OMNeT++")
                else:
                    # Reset the system time counter if we're getting valid simulation time
                    if hasattr(self, 'system_time_start'):
                        del self.system_time_start

                # Convert from milliseconds to microseconds
                current_time_us = int(current_sim_time_ms * 1000)

                # Check if it's time to process a batch
                time_since_last_batch = current_time_us - self.last_batch_process_time
                time_since_last_action = current_time_us - last_action_time_us

                # Get the most recent data timestamp from the environment
                data_timestamp_ms = 0
                using_system_time_for_data = False
                with self.env.lock:
                    for queue_id in self.env.expected_queues:
                        if queue_id in self.env.queue_states and self.env.queue_states[queue_id]:
                            data_timestamp_ms = self.env.queue_states[queue_id].get('current_sim_time', 0)
                            break

                # Check if we have a valid simulation time or need to use system time
                # Check for Unix timestamp (seconds since epoch) which would be around 1.7 billion for current dates
                # Real simulation time should be much smaller, typically in seconds or milliseconds
                if data_timestamp_ms == 0 or data_timestamp_ms > 1000000000:  # If zero or suspiciously large (Unix timestamp)
                    # Use system time in milliseconds as a fallback
                    using_system_time_for_data = True
                    data_timestamp_ms = time.time() * 1000

                    # Check if we've been using system time for too long
                    if not hasattr(self, 'batch_system_time_start'):
                        self.batch_system_time_start = time.time()
                    elif time.time() - self.batch_system_time_start > 10:  # After 10 seconds of using system time
                        if (time.time() - self.batch_system_time_start) % 30 < 0.5:  # Log to console every 30 seconds
                            logging.warning(f"batch_processor: Using system time for {time.time() - self.batch_system_time_start:.1f} seconds - no valid simulation time from OMNeT++")
                            # print(f"WARNING: batch_processor: Using system time for {time.time() - self.batch_system_time_start:.1f} seconds - no valid simulation time from OMNeT++")
                else:
                    # Reset the system time counter if we're getting valid simulation time
                    if hasattr(self, 'batch_system_time_start'):
                        del self.batch_system_time_start

                # Convert from milliseconds to microseconds
                data_timestamp_us = int(data_timestamp_ms * 1000)

                # Update the last valid simulation time if this is newer and not system time
                if not using_system_time_for_data and data_timestamp_us > self.last_valid_sim_time_us:
                    self.last_valid_sim_time_us = data_timestamp_us

                # Check if the data is fresh enough
                data_age_us = current_time_us - data_timestamp_us
                data_is_fresh = data_timestamp_us > 0 and (not self.data_age_check_enabled or data_age_us <= self.max_data_age_us)

                # Check if we should process this batch
                should_process = (
                    self.observation_batches and
                    len(self.observation_batches) >= min_batch_size and
                    time_since_last_batch >= self.batch_interval_us and
                    data_is_fresh  # Only process if data is fresh or age checking is disabled
                )

                # Check if we should send an action
                can_send_action = time_since_last_action >= min_action_interval_us

                # Log if we're skipping due to stale data
                if (self.observation_batches and
                    len(self.observation_batches) >= min_batch_size and
                    time_since_last_batch >= self.batch_interval_us):

                    if not data_is_fresh and self.data_age_check_enabled:
                        logging.info(f"Skipping batch processing due to stale data: age={data_age_us/1000:.1f}ms, max allowed={self.max_data_age_us/1000:.1f}ms")
                        if data_age_us > self.max_data_age_us * 10:  # Only print to console for very stale data
                            logging.info(f"[{current_time_str}] Skipping batch processing due to stale data: age={data_age_us/1000:.1f}ms, max allowed={self.max_data_age_us/1000:.1f}ms")
                    elif data_timestamp_us == 0:
                        logging.warning(f"[{current_time_str}] No valid simulation time available - waiting for data from OMNeT++")
                        # print(f"[{current_time_str}] No valid simulation time available - waiting for data from OMNeT++")

                if should_process:
                    # Make a copy of the batch data with the lock
                    with self.lock:
                        if not self.observation_batches:  # Double-check in case it was emptied
                            continue

                        batch_to_process = self.observation_batches.copy()
                        self.observation_batches = []
                        self.last_batch_process_time = current_time_us  # Using simulation time

                    # Log batch size
                    batch_size = len(batch_to_process)

                    if not can_send_action:
                        # If we can't send an action yet, just log and continue
                        skipped_batches += 1
                        if skipped_batches % 5 == 0:  # Log every 5 skipped batches
                            logging.debug(f"Skipped {skipped_batches} batches - waiting for action interval ({time_since_last_action/1000:.1f}/{min_action_interval_us/1000:.1f} ms)")
                        continue

                    # Reset skipped batches counter
                    if skipped_batches > 0:
                        logging.debug(f"Skipped {skipped_batches} batches due to rate limiting")
                        skipped_batches = 0

                    logging.info(f"Processing batch with {batch_size} observations after {time_since_last_batch/1000:.2f} ms")
                    # print(f"Processing batch with {batch_size} observations")

                    if batch_size > 0:
                        # Compute average observation
                        avg_observation = np.mean(batch_to_process, axis=0)

                        # Check if this observation is significantly different from the last one
                        # to avoid sending duplicate actions
                        if last_observation_hash is not None:
                            # Convert to numpy array for comparison
                            current_obs_array = avg_observation

                            # Check if the observation has changed significantly
                            # This is a more sophisticated check than just hashing
                            if np.all(np.abs(current_obs_array) < 1e-6):
                                # If all values are close to zero, use absolute difference
                                is_similar = True  # Consider zero observations as similar
                            else:
                                # Compute relative difference for non-zero observations
                                # Skip the check if we don't have a previous observation
                                is_similar = False

                            if is_similar:
                                logging.debug("Skipping action computation - observation too similar to previous")
                                continue

                        # Update the hash for the next comparison
                        current_obs_hash = hash(avg_observation.tobytes())
                        last_observation_hash = current_obs_hash

                        # Create a batch dimension (batch size = 1)
                        observation_batch = tf.convert_to_tensor([avg_observation], dtype=tf.float32)

                        # Get action directly from actor network
                        try:
                            # Actor networks typically take observations directly and return action distributions
                            # We'll call the actor network and sample from the distribution
                            action_distribution = self.actor_network(observation_batch)

                            # Sample an action from the distribution
                            if hasattr(action_distribution, 'sample'):
                                action = action_distribution.sample().numpy()[0]  # Extract from batch
                            elif hasattr(action_distribution, 'mode'):
                                # Use the mode (deterministic action) if available
                                action = action_distribution.mode().numpy()[0]
                            else:
                                # If it's just a tensor, use it directly
                                action = action_distribution.numpy()[0]

                            # Update the last action time using simulation time
                            last_action_time_us = current_time_us

                            # Update action statistics
                            total_actions_sent += 1
                            self.actions_sent += 1  # Track in instance
                            current_time = time.time()
                            current_time_str = time.strftime("%H:%M:%S", time.localtime())

                            # Log the action with simulation timestamp and time since last action
                            time_since_last_ms = time_since_last_action / 1000
                            sim_time_sec = current_sim_time_ms / 1000  # Convert to seconds for logging
                            logging.info(f"[{current_time_str}] Action #{total_actions_sent} at sim time {sim_time_sec:.3f}s ({time_since_last_ms:.1f} ms since last): {action}")
                            # print(f"[{current_time_str}] Action #{total_actions_sent} (sim time {sim_time_sec:.3f}s, batch size {batch_size}, {time_since_last_ms:.1f} ms since last): {action}")

                            # Update the environment's GCL configuration
                            self.env.gcl_config = action

                            # Send action to all queues using the environment's method
                            queue_count = 0
                            for queue_id in self.env.expected_queues:
                                self.env._send_action_to_omnet(queue_id, action)
                                queue_count += 1

                            logging.info(f"[{current_time_str}] Action sent to {queue_count} queues")

                            # Log action rate statistics periodically
                            if current_time - last_stats_time > stats_interval:
                                elapsed_time = current_time - action_start_time
                                if elapsed_time > 0:
                                    actions_per_sec = total_actions_sent / elapsed_time
                                    logging.info(f"[{current_time_str}] Action rate statistics: {total_actions_sent} actions in {elapsed_time:.1f} seconds ({actions_per_sec:.2f} actions/sec)")
                                    # print(f"[{current_time_str}] Action rate statistics: {total_actions_sent} actions in {elapsed_time:.1f} seconds ({actions_per_sec:.2f} actions/sec)")
                                last_stats_time = current_time

                            # Increment episode step
                            self.current_episode_step += 1
                            if self.current_episode_step >= self.max_episode_steps:
                                logging.info(f"[{current_time_str}] Max episode steps reached. Resetting episode.")
                                # print(f"[{current_time_str}] Max episode steps reached. Resetting episode.")
                                self.current_episode_step = 0
                                self.is_first_step = True

                        except Exception as e:
                            current_time_str = time.strftime("%H:%M:%S", time.localtime())
                            logging.error(f"[{current_time_str}] Error getting action from actor network: {e}")
                            # print(f"[{current_time_str}] Error getting action from actor network: {e}")
                            import traceback
                            logging.error(traceback.format_exc())

                            # Reset episode state and try again
                            self.is_first_step = True
                            logging.info(f"[{current_time_str}] Reset episode state after error")
                            # print(f"[{current_time_str}] Reset episode state after error")

                # Sleep for a short time to avoid busy waiting
                # Use a shorter sleep time than the batch interval to ensure timely processing
                # but not too short to avoid excessive CPU usage
                sleep_time = max(self.batch_interval_us / 5000, 1000) / 1000000  # Convert to seconds with minimum of 1ms
                time.sleep(sleep_time)

            except Exception as e:
                current_time_str = time.strftime("%H:%M:%S", time.localtime())
                logging.error(f"[{current_time_str}] Error in batch processor: {e}")
                # print(f"[{current_time_str}] Error in batch processor: {e}")
                import traceback
                logging.error(traceback.format_exc())
                # Sleep briefly to avoid tight error loops
                time.sleep(0.01)

    def _schedule_connection_check(self):
        """Schedule a periodic check of the connection status."""
        if not self.running:
            return

        # Create a thread to check connection status every 5 seconds
        check_thread = threading.Thread(target=self._check_connection_status)
        check_thread.daemon = True
        check_thread.start()

    def _check_connection_status(self):
        """Check if we're receiving data and take corrective action if needed."""
        try:
            # Wait 5 seconds before checking
            time.sleep(5)

            if not self.running:
                return

            current_time = time.time()
            current_time_str = time.strftime("%H:%M:%S", time.localtime())

            # Check if we've received any new messages since the last check
            new_messages = self.total_messages_received - self.last_message_count
            time_since_last_check = current_time - self.last_message_check_time

            # Update counters for next check
            self.last_message_count = self.total_messages_received
            self.last_message_check_time = current_time

            # Calculate message rate
            message_rate = new_messages / time_since_last_check if time_since_last_check > 0 else 0

            # Log connection status
            if new_messages > 0:
                logging.info(f"[{current_time_str}] Connection active: {new_messages} new messages received ({message_rate:.1f} msgs/sec)")

                # If we're receiving data again, re-enable data age checking
                if not self.data_age_check_enabled:
                    self.data_age_check_enabled = True
                    logging.info(f"[{current_time_str}] Data flow resumed - re-enabling data age checking")
                    # print(f"[{current_time_str}] Data flow resumed - re-enabling data age checking")
            else:
                logging.warning(f"[{current_time_str}] No new messages received in the last {time_since_last_check:.1f} seconds")
                # print(f"[{current_time_str}] WARNING: No new messages received in the last {time_since_last_check:.1f} seconds")

                # Check if we're using system time instead of simulation time
                using_system_time = False
                if hasattr(self, 'system_time_start') or hasattr(self, 'run_system_time_start') or hasattr(self, 'batch_system_time_start'):
                    using_system_time = True
                    logging.warning(f"[{current_time_str}] Using system time instead of simulation time from OMNeT++")
                    # print(f"[{current_time_str}] WARNING: Using system time instead of simulation time from OMNeT++")

                # If we haven't received data for a while, try to reset the connection
                if time_since_last_check > 10 or using_system_time:  # If no data for 10+ seconds or using system time
                    # Temporarily disable data age checking to allow any data to come through
                    if self.data_age_check_enabled:
                        self.data_age_check_enabled = False
                        logging.warning(f"[{current_time_str}] Temporarily disabling data age checking to allow any data through")
                        # print(f"[{current_time_str}] Temporarily disabling data age checking to allow any data through")

                    # First try to directly check the socket and receive data
                    logging.info(f"[{current_time_str}] Checking ZMQ socket status...")
                    # print(f"[{current_time_str}] Checking ZMQ socket status...")

                    socket_ok = self._check_zmq_socket()

                    if socket_ok:
                        logging.info(f"[{current_time_str}] ZMQ socket is working - received data directly")
                        # print(f"[{current_time_str}] ZMQ socket is working - received data directly")
                    else:
                        # If direct check failed, try to reset the connection
                        try:
                            logging.info(f"[{current_time_str}] Attempting to reset ZMQ connection...")
                            # print(f"[{current_time_str}] Attempting to reset ZMQ connection...")

                            # Access the socket directly to check its status
                            if hasattr(self.env, 'socket') and self.env.socket:
                                # Force a reconnect by closing and reopening the socket
                                old_port = self.env.port
                                self.env.close()
                                time.sleep(1)

                                # Recreate the environment with the same port
                                self.env = TSNGCLEnvironment(port=old_port)
                                self.env.data_active = True
                                self.env.last_data_received_time = time.time()

                                logging.info(f"[{current_time_str}] ZMQ connection reset successful")
                                # print(f"[{current_time_str}] ZMQ connection reset successful")

                                # Try to check the socket again after reset
                                time.sleep(1)
                                socket_ok = self._check_zmq_socket()
                                if socket_ok:
                                    logging.info(f"[{current_time_str}] ZMQ socket is working after reset")
                                    # print(f"[{current_time_str}] ZMQ socket is working after reset")
                        except Exception as e:
                            logging.error(f"[{current_time_str}] Error resetting ZMQ connection: {e}")
                            # print(f"[{current_time_str}] Error resetting ZMQ connection: {e}")

            # Schedule the next check
            self._schedule_connection_check()

        except Exception as e:
            logging.error(f"Error in connection check: {e}")
            import traceback
            logging.error(traceback.format_exc())

            # Try to reschedule even if there was an error
            if self.running:
                time.sleep(5)
                self._schedule_connection_check()

    def _log_queue_statistics(self):
        """Log detailed statistics about the queues."""
        if not hasattr(self.env, 'queue_lengths') or not self.env.data_active:
            return

        try:
            # Get current time for timestamp
            current_time_str = time.strftime("%H:%M:%S", time.localtime())

            # Log statistics for each queue
            for i, queue_id in enumerate(self.env.expected_queues):
                if i < len(self.env.queue_lengths):
                    length = self.env.queue_lengths[i]
                    occupancy = self.env.queue_occupancy[i] if hasattr(self.env, 'queue_occupancy') and i < len(self.env.queue_occupancy) else 0
                    rate = self.env.arrival_rates[i] if hasattr(self.env, 'arrival_rates') and i < len(self.env.arrival_rates) else 0

                    logging.info(f"[{current_time_str}] Queue {queue_id} stats: length={length:.1f}, occupancy={occupancy:.2f}, arrival_rate={rate:.1f}")
                    # print(f"[{current_time_str}] Queue {queue_id} stats: length={length:.1f}, occupancy={occupancy:.2f}, arrival_rate={rate:.1f}")
        except Exception as e:
            logging.error(f"Error logging queue statistics: {e}")
            # Don't print to avoid cluttering the terminal

    def run(self):
        """Run the policy with live data from OMNeT++.

        This method continuously collects observations from the environment
        and adds them to the batch. The batch processor thread handles
        processing the batches and sending actions.
        """
        current_time_str = time.strftime("%H:%M:%S", time.localtime())
        logging.info(f"[{current_time_str}] Starting policy execution...")
        # print(f"[{current_time_str}] Starting policy execution...")
        # print(f"[{current_time_str}] Waiting for data from OMNeT++...")

        # Initialize the last batch process time with simulation time
        # Start with 0, will be updated when we get the first simulation time
        self.last_batch_process_time = 0

        # Track the last time we collected an observation (in simulation time)
        last_observation_time = 0

        # Track data status changes to avoid redundant logging
        last_data_status = False

        # Track when we last logged information
        last_log_time = time.time()

        # Minimum time between collecting observations (in microseconds)
        # This prevents collecting too many observations too quickly
        min_observation_interval_us = 100  # 100 microseconds

        # Maximum batch size to prevent memory issues
        max_batch_size = 100

        try:
            while True:
                # Get current time for timestamp
                current_time = time.time()
                current_time_str = time.strftime("%H:%M:%S", time.localtime())

                # Always keep data_active as true to ensure we process data
                self.env.data_active = True
                self.env.last_data_received_time = time.time()

                # Only log status changes for consistency in output
                if not last_data_status:
                    logging.info(f"[{current_time_str}] Agent active and processing data from OMNeT++.")
                    # print(f"[{current_time_str}] Agent active and processing data from OMNeT++.")
                    last_data_status = True

                # Periodically log that we're still active
                if current_time - last_log_time > 10.0:  # Log every 10 seconds
                    logging.info(f"[{current_time_str}] Agent still active and processing data...")
                    # Don't print to console to avoid cluttering
                    last_log_time = current_time

                # Get current simulation time in microseconds
                current_sim_time_ms = 0
                using_system_time = False
                with self.env.lock:
                    for queue_id in self.env.expected_queues:
                        if queue_id in self.env.queue_states and self.env.queue_states[queue_id]:
                            current_sim_time_ms = self.env.queue_states[queue_id].get('current_sim_time', 0)
                            break

                # If we don't have valid simulation time yet, use system time as a fallback
                # This ensures we don't get stuck waiting for simulation time
                # Check for Unix timestamp (seconds since epoch) which would be around 1.7 billion for current dates
                # Real simulation time should be much smaller, typically in seconds or milliseconds
                if current_sim_time_ms == 0 or current_sim_time_ms > 1000000000:  # If zero or suspiciously large (Unix timestamp)
                    # Use system time in milliseconds as a fallback
                    using_system_time = True
                    current_sim_time_ms = time.time() * 1000
                    logging.debug("No valid simulation time available in run(), using system time as fallback")

                    # Check if we've been using system time for too long
                    if not hasattr(self, 'run_system_time_start'):
                        self.run_system_time_start = time.time()
                    elif time.time() - self.run_system_time_start > 10:  # After 10 seconds of using system time
                        logging.warning(f"run(): Using system time for {time.time() - self.run_system_time_start:.1f} seconds - no valid simulation time from OMNeT++")
                        if (time.time() - self.run_system_time_start) % 30 < 0.5:  # Log to console every 30 seconds
                            print(f"WARNING: run(): Using system time for {time.time() - self.run_system_time_start:.1f} seconds - no valid simulation time from OMNeT++")
                else:
                    # Reset the system time counter if we're getting valid simulation time
                    if hasattr(self, 'run_system_time_start'):
                        del self.run_system_time_start

                # Convert from milliseconds to microseconds
                current_time_us = int(current_sim_time_ms * 1000)

                # Log simulation time periodically
                if current_time - last_log_time > 2.0:
                    sim_time_sec = current_sim_time_ms / 1000.0

                    if using_system_time:
                        logging.info(f"[{current_time_str}] Using SYSTEM TIME (no valid simulation time): {sim_time_sec:.6f} seconds")
                        # print(f"[{current_time_str}] Using SYSTEM TIME (no valid simulation time): {sim_time_sec:.6f} seconds")
                    else:
                        logging.info(f"[{current_time_str}] Current simulation time from OMNeT++: {sim_time_sec:.6f} seconds")
                        # print(f"[{current_time_str}] Current simulation time from OMNeT++: {sim_time_sec:.6f} seconds")

                    last_log_time = current_time

                    # Also log queue statistics periodically
                    self._log_queue_statistics()

                # Check if it's time to collect a new observation
                if current_time_us - last_observation_time >= min_observation_interval_us:
                    # Get the most recent data timestamp from the environment
                    data_timestamp_ms = 0
                    using_system_time_for_data = False
                    with self.env.lock:
                        for queue_id in self.env.expected_queues:
                            if queue_id in self.env.queue_states and self.env.queue_states[queue_id]:
                                data_timestamp_ms = self.env.queue_states[queue_id].get('current_sim_time', 0)
                                break

                    # Check if we have a valid simulation time or need to use system time
                    # Check for Unix timestamp (seconds since epoch) which would be around 1.7 billion for current dates
                    # Real simulation time should be much smaller, typically in seconds or milliseconds
                    if data_timestamp_ms == 0 or data_timestamp_ms > 1000000000:  # If zero or suspiciously large (Unix timestamp)
                        # Use system time in milliseconds as a fallback
                        using_system_time_for_data = True
                        data_timestamp_ms = time.time() * 1000

                        # Log this condition periodically
                        if using_system_time and (time.time() - self.run_system_time_start) % 30 < 0.5:  # Log every 30 seconds
                            logging.warning(f"Using system time for data timestamp in run() - no valid simulation time from OMNeT++")

                    # Convert from milliseconds to microseconds
                    data_timestamp_us = int(data_timestamp_ms * 1000)

                    # Update the last valid simulation time if this is newer and not system time
                    if not using_system_time_for_data and data_timestamp_us > self.last_valid_sim_time_us:
                        self.last_valid_sim_time_us = data_timestamp_us

                    # Check if the data is fresh enough
                    data_age_us = current_time_us - data_timestamp_us

                    # Increment message counter whenever we receive any data
                    if data_timestamp_us > 0 and not using_system_time_for_data:
                        self.total_messages_received += 1

                    # Process data if it's fresh enough OR if data age checking is disabled
                    # Also allow processing if we're using system time and data age checking is disabled
                    if ((data_timestamp_us > 0 and (not self.data_age_check_enabled or data_age_us <= self.max_data_age_us)) or
                        (using_system_time_for_data and not self.data_age_check_enabled)):
                        # Get the current observation from the environment
                        observation = self.env._get_flattened_observation()
                        self.observations_processed += 1  # Track observations

                        # Update the last observation time with simulation time
                        last_observation_time = current_time_us

                        # Add the observation to the batch with the lock
                        with self.lock:
                            # Only add if we haven't reached the maximum batch size
                            if len(self.observation_batches) < max_batch_size:
                                self.observation_batches.append(observation)
                                batch_size = len(self.observation_batches)

                                # Log batch growth periodically
                                if batch_size % 10 == 0:  # Log every 10 observations
                                    logging.info(f"[{current_time_str}] Added observation to batch, current size: {batch_size}")
                                    if batch_size == 10:  # First time reaching 10
                                        print(f"[{current_time_str}] Collecting observations: batch size {batch_size}")
                                    elif batch_size % 50 == 0:  # Log to console at larger intervals
                                        print(f"[{current_time_str}] Collecting observations: batch size {batch_size}")
                    else:
                        # Log that we're rejecting stale data
                        if data_timestamp_us > 0:  # Only log if we actually have data
                            self.data_rejections += 1  # Track rejections
                            logging.info(f"[{current_time_str}] Rejecting stale data: age={data_age_us/1000:.1f}ms, max allowed={self.max_data_age_us/1000:.1f}ms")
                            if data_age_us > self.max_data_age_us * 10:  # Only print to console for very stale data
                                print(f"[{current_time_str}] Rejecting stale data: age={data_age_us/1000:.1f}ms, max allowed={self.max_data_age_us/1000:.1f}ms")

                # Sleep a very short time to avoid busy waiting
                # This is much shorter than the observation interval
                time.sleep(min_observation_interval_us / 2000000)  # Convert to seconds and divide by 2

        except KeyboardInterrupt:
            current_time_str = time.strftime("%H:%M:%S", time.localtime())
            logging.info(f"[{current_time_str}] Execution interrupted by user.")
            # print(f"\n[{current_time_str}] Execution interrupted by user.")
        except Exception as e:
            current_time_str = time.strftime("%H:%M:%S", time.localtime())
            logging.error(f"[{current_time_str}] Error during policy execution: {e}")
            # print(f"[{current_time_str}] Error during policy execution: {e}")
            import traceback
            traceback.print_exc()
            logging.error(traceback.format_exc())
        finally:
            self.close()

    def save_execution_metrics(self, model_metadata=None):
        """Save detailed execution metrics to a file."""
        end_time = time.time()
        execution_duration = end_time - self.execution_start_time

        metrics = {
            'execution_summary': {
                'start_time': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(self.execution_start_time)),
                'end_time': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_time)),
                'duration_seconds': execution_duration,
                'duration_minutes': execution_duration / 60,
                'duration_hours': execution_duration / 3600
            },
            'performance_metrics': {
                'total_actions_sent': self.actions_sent,
                'total_observations_processed': self.observations_processed,
                'total_data_rejections': self.data_rejections,
                'connection_resets': self.connection_resets,
                'actions_per_second': self.actions_sent / execution_duration if execution_duration > 0 else 0,
                'observations_per_second': self.observations_processed / execution_duration if execution_duration > 0 else 0,
                'data_rejection_rate': self.data_rejections / (self.observations_processed + self.data_rejections) if (self.observations_processed + self.data_rejections) > 0 else 0
            },
            'configuration': {
                'batch_interval_us': self.batch_interval_us,
                'action_rate_multiplier': self.action_rate_multiplier,
                'max_data_age_us': self.max_data_age_us,
                'effective_action_interval_ms': (self.batch_interval_us * self.action_rate_multiplier) / 1000
            },
            'model_information': model_metadata if model_metadata else {},
            'environment_stats': {
                'expected_queues': list(self.env.expected_queues) if hasattr(self.env, 'expected_queues') else [],
                'num_queues': self.env.num_queues if hasattr(self.env, 'num_queues') else 0
            }
        }

        # Save to file
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"policy_execution_metrics_{timestamp}.json"

        try:
            with open(filename, 'w') as f:
                json.dump(metrics, f, indent=4)
            print(f"\nExecution metrics saved to: {filename}")
            logging.info(f"Execution metrics saved to: {filename}")

            # Print summary
            print(f"\n{'='*60}")
            print("EXECUTION SUMMARY")
            print(f"{'='*60}")
            print(f"Duration: {execution_duration:.2f} seconds ({execution_duration/60:.2f} minutes)")
            print(f"Actions Sent: {self.actions_sent}")
            print(f"Observations Processed: {self.observations_processed}")
            print(f"Data Rejections: {self.data_rejections}")
            print(f"Actions per Second: {metrics['performance_metrics']['actions_per_second']:.2f}")
            print(f"Observations per Second: {metrics['performance_metrics']['observations_per_second']:.2f}")
            print(f"Data Rejection Rate: {metrics['performance_metrics']['data_rejection_rate']:.2%}")
            print(f"{'='*60}")

        except Exception as e:
            print(f"Error saving execution metrics: {e}")
            logging.error(f"Error saving execution metrics: {e}")

    def _check_zmq_socket(self):
        """Directly check the ZMQ socket status and attempt to receive data.

        This is a more direct approach to check if the socket is working properly.
        """
        current_time_str = time.strftime("%H:%M:%S", time.localtime())

        if not hasattr(self.env, 'socket') or not self.env.socket:
            logging.warning(f"[{current_time_str}] ZMQ socket not initialized")
            # print(f"[{current_time_str}] ZMQ socket not initialized")
            return False

        try:
            # Try to directly receive from the socket with a short timeout
            import zmq

            # Check socket events first
            try:
                if hasattr(self.env.socket, 'get_events'):
                    events = self.env.socket.get_events()
                    logging.info(f"[{current_time_str}] ZMQ socket events: {events}")
                    # print(f"[{current_time_str}] ZMQ socket events: {events}")

                    # Check if socket has events indicating it's ready to receive
                    if events & zmq.POLLIN:
                        logging.info(f"[{current_time_str}] ZMQ socket has POLLIN event - ready to receive")
                        # print(f"[{current_time_str}] ZMQ socket has POLLIN event - ready to receive")
            except Exception as events_err:
                logging.warning(f"[{current_time_str}] Error checking socket events: {events_err}")

            # Create a poller for more reliable timeout handling
            poller = zmq.Poller()
            poller.register(self.env.socket, zmq.POLLIN)

            # Try to receive a message with a short timeout
            logging.info(f"[{current_time_str}] Attempting direct ZMQ receive with poller (100ms timeout)...")
            # print(f"[{current_time_str}] Attempting direct ZMQ receive with poller (100ms timeout)...")

            # Poll with a 100ms timeout
            socks = dict(poller.poll(100))

            if self.env.socket in socks and socks[self.env.socket] == zmq.POLLIN:
                # Socket has data available
                try:
                    # Use recv_multipart for ROUTER socket
                    frames = self.env.socket.recv_multipart(flags=zmq.NOBLOCK)
                    if len(frames) >= 3:  # ROUTER frames: [identity, empty, message]
                        identity = frames[0]
                        message = frames[2].decode('utf-8')
                        logging.info(f"[{current_time_str}] Successfully received ZMQ message from {identity}: {message[:50]}...")
                        # print(f"[{current_time_str}] Successfully received ZMQ message")

                        # Process the received message if the environment has a method for it
                        if hasattr(self.env, '_process_message'):
                            try:
                                self.env._process_message(message)
                                logging.info(f"[{current_time_str}] Successfully processed ZMQ message")
                                # print(f"[{current_time_str}] Successfully processed ZMQ message")
                                return True
                            except Exception as e:
                                logging.error(f"[{current_time_str}] Error processing ZMQ message: {e}")
                                # print(f"[{current_time_str}] Error processing ZMQ message: {e}")
                        else:
                            logging.warning(f"[{current_time_str}] Environment doesn't have _process_message method")
                            # print(f"[{current_time_str}] Environment doesn't have _process_message method")
                    else:
                        logging.warning(f"[{current_time_str}] Received incomplete message frames: {len(frames)}")
                        # print(f"[{current_time_str}] Received incomplete message frames: {len(frames)}")
                except zmq.Again:
                    logging.warning(f"[{current_time_str}] No ZMQ message available (NOBLOCK flag)")
                    # print(f"[{current_time_str}] No ZMQ message available (NOBLOCK flag)")
            else:
                logging.warning(f"[{current_time_str}] No ZMQ message available (poll timeout)")
                # print(f"[{current_time_str}] No ZMQ message available (poll timeout)")

                # Try to send a ping message to check if the socket is working
                try:
                    logging.info(f"[{current_time_str}] Sending ping message to check socket...")
                    # print(f"[{current_time_str}] Sending ping message to check socket...")

                    # Only try to send if we have active queues
                    if hasattr(self.env, 'expected_queues') and self.env.expected_queues:
                        queue_id = list(self.env.expected_queues)[0]
                        if hasattr(self.env, '_send_action_to_omnet'):
                            # Use a dummy action
                            dummy_action = np.array([4.0, 3.0, 2.0, 1.0])
                            self.env._send_action_to_omnet(queue_id, dummy_action)
                            logging.info(f"[{current_time_str}] Ping message sent to queue {queue_id}")
                            # print(f"[{current_time_str}] Ping message sent to queue {queue_id}")
                except Exception as ping_err:
                    logging.warning(f"[{current_time_str}] Error sending ping message: {ping_err}")

            # Unregister from poller
            poller.unregister(self.env.socket)

            return False
        except Exception as e:
            logging.error(f"[{current_time_str}] Error checking ZMQ socket: {e}")
            # print(f"[{current_time_str}] Error checking ZMQ socket: {e}")
            import traceback
            logging.error(traceback.format_exc())
            return False

    def close(self):
        """Close the PolicyRunner and clean up resources."""
        current_time_str = time.strftime("%H:%M:%S", time.localtime())
        logging.info(f"[{current_time_str}] Closing PolicyRunner...")
        # print(f"[{current_time_str}] Closing PolicyRunner...")

        # Stop the batch processing thread
        self.running = False

        # Wait for the batch thread to finish
        if hasattr(self, 'batch_thread') and self.batch_thread.is_alive():
            try:
                self.batch_thread.join(timeout=1.0)
                logging.info(f"[{current_time_str}] Batch processor thread stopped")
                # print(f"[{current_time_str}] Batch processor thread stopped")
            except Exception as e:
                logging.error(f"[{current_time_str}] Error stopping batch thread: {e}")
                # print(f"[{current_time_str}] Error stopping batch thread: {e}")

        # Close the environment with robust error handling
        if hasattr(self, 'env'):
            try:
                # First try to close the socket directly
                if hasattr(self.env, 'socket') and self.env.socket:
                    try:
                        self.env.socket.close(linger=0)  # Don't wait for pending messages
                        logging.info(f"[{current_time_str}] ZMQ socket closed")
                    except Exception as socket_err:
                        logging.error(f"[{current_time_str}] Error closing ZMQ socket: {socket_err}")

                # Then try to terminate the context
                if hasattr(self.env, 'context') and self.env.context:
                    try:
                        self.env.context.term()
                        logging.info(f"[{current_time_str}] ZMQ context terminated")
                    except Exception as context_err:
                        logging.error(f"[{current_time_str}] Error terminating ZMQ context: {context_err}")

                # Finally close the environment
                try:
                    self.env.close()
                    logging.info(f"[{current_time_str}] Environment closed")
                    # print(f"[{current_time_str}] Environment closed")
                except Exception as env_err:
                    logging.error(f"[{current_time_str}] Error closing environment: {env_err}")
                    # print(f"[{current_time_str}] Error closing environment: {env_err}")
            except Exception as e:
                logging.error(f"[{current_time_str}] Error during environment cleanup: {e}")
                # print(f"[{current_time_str}] Error during environment cleanup: {e}")

        # Additional ZMQ cleanup
        try:
            import zmq
            import gc

            # Try to clean up the global ZMQ context
            try:
                context = zmq.Context.instance()
                context.term()
                time.sleep(1)
                zmq._context_initialized = False
                logging.info(f"[{current_time_str}] Global ZMQ context terminated")
            except Exception as zmq_err:
                logging.warning(f"[{current_time_str}] Error terminating global ZMQ context: {zmq_err}")

            # Force garbage collection
            gc.collect()

            # Reset ZMQ state more aggressively
            if hasattr(zmq.Context, '_instance'):
                zmq.Context._instance = None
                logging.info(f"[{current_time_str}] ZMQ context instance reset")
        except Exception as zmq_cleanup_err:
            logging.warning(f"[{current_time_str}] Error during additional ZMQ cleanup: {zmq_cleanup_err}")

        logging.info(f"[{current_time_str}] PolicyRunner closed.")
        print(f"[{current_time_str}] PolicyRunner closed.")

def find_best_saved_model():
    """Find the best saved model from the saved_models directory."""
    saved_models_dir = "saved_models"

    if not os.path.exists(saved_models_dir):
        return None, None

    # Look for session directories
    session_dirs = glob.glob(os.path.join(saved_models_dir, "session_*"))

    if not session_dirs:
        return None, None

    best_model_path = None
    best_metadata = None
    best_return = float('-inf')

    for session_dir in session_dirs:
        # Check if this session has the required files
        actor_path = os.path.join(session_dir, "actor_network")
        metadata_path = os.path.join(session_dir, "optuna_metadata.json")
        detailed_metrics_path = os.path.join(session_dir, "detailed_metrics.json")

        if not os.path.exists(actor_path):
            continue

        # Load metadata if available
        metadata = {}
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
            except Exception as e:
                print(f"Warning: Could not load metadata from {metadata_path}: {e}")
                continue

        # Load detailed metrics if available
        detailed_metrics = {}
        if os.path.exists(detailed_metrics_path):
            try:
                with open(detailed_metrics_path, 'r') as f:
                    detailed_metrics = json.load(f)
            except Exception as e:
                print(f"Warning: Could not load detailed metrics from {detailed_metrics_path}: {e}")

        # Determine the best return for this model
        model_return = float('-inf')

        # Try to get return from detailed metrics first
        if 'best_return' in detailed_metrics and detailed_metrics['best_return'] is not None:
            model_return = detailed_metrics['best_return']
        elif 'final_return' in detailed_metrics and detailed_metrics['final_return'] is not None:
            model_return = detailed_metrics['final_return']
        elif 'best_value' in metadata and metadata['best_value'] is not None:
            model_return = metadata['best_value']
        else:
            # Skip this model if we can't determine its performance
            continue

        # Update best model if this one is better
        if model_return > best_return:
            best_return = model_return
            best_model_path = session_dir  # Return session directory, not actor_path
            best_metadata = {
                'session_dir': session_dir,
                'optuna_metadata': metadata,
                'detailed_metrics': detailed_metrics,
                'model_return': model_return
            }

    return best_model_path, best_metadata

def print_model_info(model_path, metadata):
    """Print information about the selected model."""
    if not metadata:
        print(f"Selected model: {model_path}")
        return

    print(f"\n{'='*60}")
    print("SELECTED MODEL INFORMATION")
    print(f"{'='*60}")
    print(f"Model Path: {model_path}")
    print(f"Session Directory: {metadata.get('session_dir', 'Unknown')}")
    print(f"Model Return: {metadata.get('model_return', 'Unknown')}")

    # Print Optuna metadata if available
    optuna_meta = metadata.get('optuna_metadata', {})
    if optuna_meta:
        print(f"Optuna Trial: #{optuna_meta.get('trial_number', 'Unknown')}")
        print(f"Optuna Value: {optuna_meta.get('best_value', 'Unknown')}")
        print(f"Total Trials: {optuna_meta.get('total_trials', 'Unknown')}")

    # Print detailed metrics if available
    detailed_metrics = metadata.get('detailed_metrics', {})
    if detailed_metrics:
        print(f"Training Duration: {detailed_metrics.get('training_duration_seconds', 'Unknown')} seconds")
        print(f"Total Iterations: {detailed_metrics.get('total_iterations', 'Unknown')}")
        print(f"Final Return: {detailed_metrics.get('final_return', 'Unknown')}")
        print(f"Best Return: {detailed_metrics.get('best_return', 'Unknown')}")
        print(f"Mean Return: {detailed_metrics.get('mean_return', 'Unknown')}")

        # Print hyperparameters if available
        hyperparams = detailed_metrics.get('hyperparameters_used', {})
        if hyperparams:
            print("\nHyperparameters:")
            for key, value in hyperparams.items():
                print(f"  {key}: {value}")

    print(f"{'='*60}")

def main():
    """Main function to run the trained policy."""
    print("\n===== TSN GCL Policy Runner =====")
    print("This script loads a trained policy and uses it to interact with OMNeT++ in real-time.")
    print("NOTE: This script will actively process data from OMNeT++ without pausing")
    print("Data will be batched before computing and sending actions to reduce noise and improve stability")
    print("Uses simulation time when available, with system time as fallback to ensure continuous operation")
    print("Includes data freshness checking to reject outdated data from ZMQ buffer")
    print("Monitors connection status and automatically attempts recovery if data flow stops")
    print("===============================\n")

    # Clean up any existing ZMQ context to avoid "Address already in use" errors
    try:
        import zmq
        import gc

        # Try to clean up any existing ZMQ context
        try:
            zmq.Context.instance().term()
            time.sleep(2)  # Wait for resources to be released
            zmq._context_initialized = False
            print("Cleaned up existing ZMQ context")
        except Exception as e:
            print(f"Note: ZMQ context cleanup: {e}")

        # Force garbage collection
        gc.collect()
        time.sleep(1)

        # Reset ZMQ state more aggressively
        if hasattr(zmq.Context, '_instance'):
            zmq.Context._instance = None

    except Exception as e:
        print(f"Warning during ZMQ cleanup: {e}")
        # Continue anyway

    # Try to automatically find the best saved model
    print("Searching for the best saved model from Optuna training...")
    best_model_path, best_metadata = find_best_saved_model()

    if best_model_path:
        print(f"Found best model automatically!")
        print_model_info(best_model_path, best_metadata)

        # Ask user if they want to use this model
        use_auto = input("\nUse this automatically detected best model? (y/n, default: y): ").lower()
        if use_auto in ['', 'y', 'yes']:
            policy_path = best_model_path
        else:
            # Let user specify manually
            policy_path = input("Enter the path to the saved policy (session directory): ")
    else:
        print("No saved models found automatically.")
        # Get policy path from user
        policy_path = input("Enter the path to the saved policy session directory (default: saved_policies_test/best_policy): ") or "saved_policies_test/best_policy"

    # Check if policy session directory exists and contains actor_network
    if not os.path.exists(policy_path):
        print(f"Error: Policy session directory not found at {policy_path}")
        return 1

    actor_network_path = os.path.join(policy_path, "actor_network")
    if not os.path.exists(actor_network_path):
        print(f"Error: Actor network not found at {actor_network_path}")
        print(f"Make sure {policy_path} is a session directory containing an 'actor_network' subdirectory")
        return 1

    print(f"\nUsing policy: {policy_path}")

    # Get port number from user
    port_str = input("Enter the port number to use (default: 5555): ") or "5555"
    try:
        port = int(port_str)
    except ValueError:
        print(f"Invalid port number: {port_str}. Using default port 5555.")
        port = 5555

    # Get batch interval from user
    print("\nBatching Configuration:")
    print("The batch interval determines how long to collect observations before computing an action.")
    print("Longer intervals (e.g., 1000-5000 microseconds) provide more stable actions but less responsiveness.")
    print("Shorter intervals (e.g., 100-500 microseconds) provide more responsive actions but may be noisier.")
    batch_interval_str = input("Enter the batch interval in microseconds (default: 5000): ") or "5000"
    try:
        batch_interval_us = int(batch_interval_str)
        if batch_interval_us <= 0:
            print(f"Invalid batch interval: {batch_interval_str}. Using default 5000 microseconds.")
            batch_interval_us = 5000
    except ValueError:
        print(f"Invalid batch interval: {batch_interval_str}. Using default 5000 microseconds.")
        batch_interval_us = 5000

    # Get action rate multiplier from user
    print("\nAction Rate Configuration:")
    print("The action rate multiplier determines how frequently actions are sent to OMNeT++.")
    print("This is a multiplier applied to the batch interval.")
    print("Higher values (e.g., 10-20) result in fewer actions per second (more stable).")
    print("Lower values (e.g., 1-5) result in more actions per second (more responsive).")
    action_rate_str = input("Enter the action rate multiplier (default: 10): ") or "10"
    try:
        action_rate_multiplier = int(action_rate_str)
        if action_rate_multiplier <= 0:
            print(f"Invalid action rate multiplier: {action_rate_str}. Using default 10.")
            action_rate_multiplier = 10
    except ValueError:
        print(f"Invalid action rate multiplier: {action_rate_str}. Using default 10.")
        action_rate_multiplier = 10

    # Get max data age from user
    print("\nData Freshness Configuration:")
    print("The max data age determines how old data can be before it's considered stale and rejected.")
    print("This helps prevent processing outdated data from the ZMQ buffer.")
    print("Lower values (e.g., 5000-10000 microseconds) ensure more recent data but may reject valid data.")
    print("Higher values (e.g., 50000-100000 microseconds) are more lenient but may process older data.")
    max_data_age_str = input("Enter the maximum data age in microseconds (default: 10000): ") or "10000"
    try:
        max_data_age_us = int(max_data_age_str)
        if max_data_age_us <= 0:
            print(f"Invalid max data age: {max_data_age_str}. Using default 10000 microseconds.")
            max_data_age_us = 10000
    except ValueError:
        print(f"Invalid max data age: {max_data_age_str}. Using default 10000 microseconds.")
        max_data_age_us = 10000

    # Calculate and display the effective action rate
    action_interval_us = batch_interval_us * action_rate_multiplier
    action_interval_ms = action_interval_us / 1000
    actions_per_second = 1000 / action_interval_ms

    print(f"\nConfiguration Summary:")
    print(f"- Batch interval: {batch_interval_us} microseconds ({batch_interval_us/1000:.1f} ms)")
    print(f"- Action rate multiplier: {action_rate_multiplier}x")
    print(f"- Effective action interval: {action_interval_ms:.1f} milliseconds")
    print(f"- Approximate actions per second: {actions_per_second:.2f}")
    print(f"- Max data age: {max_data_age_us} microseconds ({max_data_age_us/1000:.1f} ms)")

    # Ask for confirmation if the action rate is very high
    if actions_per_second > 20:
        print("\nWARNING: The configured action rate is very high!")
        print("This may overwhelm the system and cause performance issues.")
        confirm = input("Are you sure you want to continue? (y/n): ").lower()
        if confirm != 'y' and confirm != 'yes':
            print("Aborting...")
            return 0

    # Create and run the policy runner
    max_attempts = 3
    runner = None

    # Try different ports if we encounter "Address already in use" errors
    port_options = [port, port+1, port+2, port+3, port+4]

    for attempt in range(max_attempts):
        try:
            print(f"\nConnection attempt {attempt+1}/{max_attempts}")

            # If we've had a previous failure with "Address already in use", try a different port
            current_port = port_options[min(attempt, len(port_options)-1)]
            if current_port != port:
                print(f"Trying alternate port {current_port} due to previous binding issues")

            # Additional ZMQ cleanup before each attempt
            if attempt > 0:
                try:
                    import zmq
                    zmq.Context.instance().term()
                    time.sleep(2)
                    zmq._context_initialized = False
                    print(f"Additional ZMQ cleanup before attempt {attempt+1}")
                except Exception as cleanup_err:
                    print(f"Note: Additional cleanup: {cleanup_err}")

            # Create policy runner with batch interval, action rate multiplier, and max data age
            runner = PolicyRunner(
                policy_path,
                port=current_port,
                batch_interval_us=batch_interval_us,
                action_rate_multiplier=action_rate_multiplier,
                max_data_age_us=max_data_age_us
            )

            print(f"Connection attempt {attempt+1} successful on port {current_port}")
            print("\nPolicyRunner initialized - actively processing data from OMNeT++")
            print(f"- Using port {current_port} for ZMQ communication")
            print(f"- Data will be batched every {batch_interval_us} microseconds before computing actions")
            print(f"- Actions will be sent approximately every {action_interval_ms:.1f} milliseconds")
            print(f"- Data older than {max_data_age_us/1000:.1f} ms will be rejected as stale")
            print("- Actions will be sent as soon as fresh data is available")
            print("- Will use simulation time when available, with system time as fallback")
            print("- Connection monitoring will automatically attempt recovery if data flow stops")
            print("- Data age checking will be temporarily disabled if no data is being received")
            print("\nPress Ctrl+C to stop the policy runner\n")

            # Wait for socket to initialize
            time.sleep(2)

            # Force data_active to True to ensure we process data immediately
            runner.env.data_active = True
            runner.env.last_data_received_time = time.time()
            print("Ensuring data stream is set to active state")

            # Try to directly check the ZMQ socket
            print("Checking ZMQ socket status...")
            runner._check_zmq_socket()

            break
        except Exception as e:
            print(f"Error during connection attempt {attempt+1}: {e}")
            if "Address already in use" in str(e):
                print(f"Port conflict detected. Will try a different port on next attempt.")

            if runner:
                try:
                    runner.close()
                except Exception as close_err:
                    print(f"Error closing runner: {close_err}")
            runner = None

            # Longer wait between attempts
            print(f"Waiting before next attempt...")
            time.sleep(5)

    if runner is None:
        print("Failed to create PolicyRunner after multiple attempts")
        return 1

    try:
        # Run the policy
        runner.run()
    except KeyboardInterrupt:
        print("\nExecution interrupted by user.")
    except Exception as e:
        print(f"Error during execution: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Save execution metrics before closing
        if runner:
            try:
                runner.save_execution_metrics(best_metadata)
            except Exception as metrics_err:
                print(f"Error saving metrics: {metrics_err}")

            # Make sure to close the runner
            runner.close()

    return 0

if __name__ == "__main__":
    sys.exit(main())